/**
 * Incremental‑diff sentence flusher for AISHA/Gemini → Azure TTS
 * © 2025 elatoai / MIT‑0
 * Usage:
 *   const flusher = createIncrementalFlusher((text) => speak(text));
 *   flusher.onPartial(geminiPartial);   // call for every partial text segment
 *   flusher.onDone();                   // call once when Gemini turn completes
 */

export type FlushFn = (text: string) => Promise<void>|void;

export function createIncrementalFlusher(flush: FlushFn) {
  let prev = '';               // last partial we saw
  let buffer = '';             // text we collected but haven't spoken yet

  /** remove the common prefix so we only keep the new tail */
  function tail(newText: string): string {
    let i = 0, max = Math.min(prev.length, newText.length);
    while (i < max && prev.charCodeAt(i) === newText.charCodeAt(i)) i++;
    return newText.slice(i);
  }

  /** speak any complete sentences now in the buffer */
  async function maybeFlush(final = false) {
    // Regex matches "sentence finished" punctuation in Vietnamese/English/Chinese
    const SENT_END = /([.!?。！？])\s+/;
    let idx;
    while ((idx = buffer.search(SENT_END)) !== -1) {
      const sentence = buffer.slice(0, idx + 1).trim();
      if (sentence) await flush(sentence);
      buffer = buffer.slice(idx + 1);
    }
    if (final && buffer.trim()) {
      await flush(buffer.trim());   // trailing fragment at end of turn
      buffer = '';
    }
  }

  return {
    onPartial(partial: string) {
      const addition = tail(partial);
      buffer += addition;
      prev = partial;
      maybeFlush(false);
    },
    onDone() {
      return maybeFlush(true);
    }
  };
}

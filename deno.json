{"tasks": {"lint": "deno lint", "fmt": "deno fmt", "dev": "deno run --watch main.ts"}, "compilerOptions": {}, "nodeModulesDir": "auto", "fmt": {"files": {"include": ["./**/*.ts", "./**/*.js", "./**/*.json"], "exclude": ["node_modules"]}, "options": {"useTabs": false, "lineWidth": 100, "indentWidth": 4, "singleQuote": true, "proseWrap": "preserve"}}, "lint": {"files": {"include": ["./**/*.ts"], "exclude": ["node_modules"]}, "rules": {"tags": ["recommended"], "include": ["ban-untagged-todo"], "exclude": ["no-explicit-any"]}}, "imports": {"@alexanderolsen/libsamplerate-js": "npm:@alexanderolsen/libsamplerate-js@^2.1.2", "@evan/opus": "npm:@evan/opus@^1.0.3", "@std/assert": "jsr:@std/assert@1", "@supabase/supabase-js": "npm:@supabase/supabase-js@^2.48.1", "ws": "npm:ws@^8.18.0", "@types/ws": "npm:@types/ws@^8.5.12", "jimp": "npm:jimp@^0.22.12", "msedge-tts": "npm:msedge-tts@^2.0.0"}, "deploy": {"project": "200035d0-7545-471a-8f69-14719fc34782", "exclude": ["**/node_modules"], "include": [], "entrypoint": "main.ts"}}